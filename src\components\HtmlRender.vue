<!-- HtmlRender.vue -->
<template>
  <div ref="container" />
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

const props = defineProps<{ html: string, style?: string }>()

const container = useTemplateRef('container')

onMounted(() => {
  const shadowRoot = container.value?.attachShadow({ mode: 'open' })
  if (shadowRoot) {
    shadowRoot.innerHTML = props.style ?? `
    <style>
    table {
      width: 100%;
      border-collapse: collapse;
      text-align: center;
      font-size: 14px;
      margin-top: 1rem;
    }

    th,
    td {
      border: 1px solid #e5e7eb; /* gray-200 */
      padding: 0.5rem 1rem;
    }

    thead tr {
      background-color: #f3f4f6; /* gray-100 */
      font-weight: bold;
    }

    .text-green {
      color: #16a34a; /* green-600 */
    }

    .text-orange {
      color: #f97316; /* orange-500 */
    }

    .text-red {
      color: #ef4444; /* red-500 */
    }
    </style>
    ${props.html}
  `
  }
})
</script>
