<template>
  <div ref="containerRef" class="h-full w-full">
    <!-- HTML 渲染模式 -->
    <HtmlRender v-if="isHtml" :html="props.config!" />

    <!-- 图表渲染模式 -->
    <div v-else-if="isChart" class="chart-container h-full w-full" />

    <!-- 无配置状态 -->
    <div v-else class="h-full flex items-center justify-center text-gray-400">
      暂无配置
    </div>
  </div>
</template>

<script lang="ts" setup>
import * as echarts from 'echarts'
import { computed, nextTick, onMounted, onUnmounted, watch } from 'vue'
import HtmlRender from './HtmlRender.vue'

interface Props {
  /** 图表配置对象 可能是html代码或者json字符串 */
  config?: string | null
  /** 图表主题 */
  theme?: string | object
  /** 图表初始化选项 */
  initOptions?: any
}

const props = withDefaults(defineProps<Props>(), {
  config: null,
  theme: undefined,
  initOptions: undefined,
})

const containerRef = useTemplateRef('containerRef')
let chartInstance: any = null

// 判断是否为 HTML 内容
const isHtml = computed(() => {
  return props.config ? isHtmlContent(props.config) : false
})

// 判断是否为图表内容
const isChart = computed(() => {
  return props.config && !isHtmlContent(props.config)
})

// 判断是否为 HTML 内容
function isHtmlContent(content: string): boolean {
  const trimmed = content.trim()
  return trimmed.startsWith('<') && trimmed.includes('>')
}

// 解析 JSON 配置
function parseJsonConfig(jsonString: string) {
  try {
    return JSON.parse(jsonString)
  }
  catch (error) {
    console.error('JSON 配置解析失败:', error)
    return null
  }
}

// 监听配置变化
watch(() => props.config, async (newConfig) => {
  if (!newConfig) {
    disposeChart()
    return
  }

  // 如果是 HTML 内容，直接渲染，不需要图表
  if (isHtmlContent(newConfig)) {
    disposeChart()
    return
  }

  // 如果是 JSON 配置，渲染图表
  await nextTick()
  renderChart(newConfig)
}, { immediate: true })

// 监听主题变化
watch(() => props.theme, async () => {
  if (props.config && !isHtmlContent(props.config)) {
    await nextTick()
    initChart()
    renderChart(props.config)
  }
})

// 初始化图表实例
function initChart() {
  if (!containerRef.value)
    return

  const chartContainer = containerRef.value.querySelector('.chart-container')
  if (!chartContainer)
    return

  // 清理之前的实例
  disposeChart()

  // 创建新实例
  chartInstance = echarts.init(chartContainer as HTMLElement, props.theme, props.initOptions)

  // 监听窗口大小变化
  const resizeHandler = () => {
    chartInstance?.resize()
  }
  window.addEventListener('resize', resizeHandler)

  // 保存resize处理器引用，用于清理
  chartInstance._resizeHandler = resizeHandler
}

// 渲染图表
function renderChart(jsonConfig: string) {
  if (!jsonConfig)
    return

  const config = parseJsonConfig(jsonConfig)
  if (!config)
    return

  // 确保图表实例存在
  if (!chartInstance) {
    initChart()
  }

  if (!chartInstance)
    return

  try {
    chartInstance.setOption(config, true) // true 表示不合并，直接替换
  }
  catch (error) {
    console.error('图表渲染失败:', error)
  }
}

// 清理图表实例
function disposeChart() {
  if (chartInstance) {
    // 移除resize监听器
    if (chartInstance._resizeHandler) {
      window.removeEventListener('resize', chartInstance._resizeHandler)
    }
    chartInstance.dispose()
    chartInstance = null
  }
}

// 调整图表大小
function resize() {
  chartInstance?.resize()
}

// 获取图表实例
function getChartInstance() {
  return chartInstance
}

// 组件挂载时初始化
onMounted(async () => {
  await nextTick()
  if (props.config && !isHtmlContent(props.config)) {
    renderChart(props.config)
  }
})

// 组件卸载时清理
onUnmounted(() => {
  disposeChart()
})

// 暴露方法给父组件
defineExpose({
  /** 渲染图表 */
  renderChart,
  /** 获取图表实例 */
  getChartInstance,
  /** 调整图表大小 */
  resize,
  /** 清理图表实例 */
  disposeChart,
  /** 重新初始化图表 */
  initChart,
})
</script>

<style scoped>
/* 确保容器有合适的尺寸 */
div {
  min-height: 100px;
  min-width: 100px;
}
</style>
