<template>
  <div ref="chartRef" class="h-full w-full" />
</template>

<script lang="ts" setup>
import * as echarts from 'echarts'
import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue'

interface Props {
  /** 图表配置对象 */
  config?: any
  /** 图表主题 */
  theme?: string | object
  /** 图表初始化选项 */
  initOptions?: any
}

const props = withDefaults(defineProps<Props>(), {
  theme: undefined,
  initOptions: undefined,
})

const chartRef = useTemplateRef('chartRef')
let chartInstance: any = null

// 监听配置变化，重新渲染图表
watch(() => props.config, (newConfig) => {
  if (newConfig && chartInstance) {
    renderChart(newConfig)
  }
}, { deep: true })

// 监听主题变化
watch(() => props.theme, () => {
  if (chartInstance) {
    // 主题变化需要重新初始化图表实例
    initChart()
    if (props.config) {
      renderChart(props.config)
    }
  }
})

// 初始化图表实例
function initChart() {
  if (!chartRef.value)
    return

  // 清理之前的实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 创建新实例
  chartInstance = echarts.init(chartRef.value, props.theme, props.initOptions)

  // 监听窗口大小变化
  const resizeHandler = () => {
    chartInstance?.resize()
  }
  window.addEventListener('resize', resizeHandler)

  // 保存resize处理器引用，用于清理
  chartInstance._resizeHandler = resizeHandler
}

// 渲染图表
function renderChart(config: any) {
  if (!chartInstance || !config)
    return

  try {
    // 支持传入 optionJson 属性或直接传入配置对象
    const option = config
    chartInstance.setOption(option, true) // true 表示不合并，直接替换
  }
  catch (error) {
    console.error('图表渲染失败:', error)
  }
}

// 调整图表大小
function resize() {
  chartInstance?.resize()
}

// 获取图表实例
function getChartInstance() {
  return chartInstance
}

// 清理图表实例
function dispose() {
  if (chartInstance) {
    // 移除resize监听器
    if (chartInstance._resizeHandler) {
      window.removeEventListener('resize', chartInstance._resizeHandler)
    }
    chartInstance.dispose()
    chartInstance = null
  }
}

// 组件挂载时初始化
onMounted(async () => {
  await nextTick()
  initChart()

  if (props.config) {
    renderChart(props.config)
  }
})

// 组件卸载时清理
onUnmounted(() => {
  dispose()
})

// 暴露方法给父组件
defineExpose({
  /** 渲染图表 */
  renderChart,
  /** 获取图表实例 */
  getChartInstance,
  /** 调整图表大小 */
  resize,
  /** 清理图表实例 */
  dispose,
  /** 重新初始化图表 */
  initChart,
})
</script>

<style scoped>
/* 确保容器有合适的尺寸 */
div {
  min-height: 100px;
  min-width: 100px;
}
</style>
