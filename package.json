{"name": "gx-edu-sec", "type": "module", "version": "0.1.24", "private": true, "scripts": {"serve": "vite --config ./config/vite.config.dev.ts", "dev": "vite --config ./config/vite.config.dev.ts --debug hmr", "build": "vite build --config ./config/vite.config.prod.ts", "build:test": "pnpm size-check", "doc": "ch2-doc", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "lint-staged": "lint-staged", "check:env": "node ./config/checkLocalEnv.js", "cui": "pnpm add ch2-components", "size-check": "npx vite-bundle-visualizer -c ./config/vite.config.prod.ts", "postinstall": "npx simple-git-hooks & pnpm check:env"}, "dependencies": {"@ant-design/icons-vue": "7.0.1", "@lljj/vue3-form-ant": "^1.19.0", "@microsoft/signalr": "^7.0.14", "@types/file-saver": "^2.0.7", "@types/vue3-json-viewer": "^2.2.0", "@vueuse/core": "^10.11.1", "ant-design-vue": "4.1.2", "axios": "^1.8.4", "ch2-components": "^3.4.55", "ckeditor5": "^43.3.1", "dayjs": "^1.11.13", "decimal.js": "^10.5.0", "echarts": "^5.6.0", "echarts-wordcloud": "^2.1.0", "file-saver": "^2.0.5", "iframe": "^1.0.0", "jsqr": "^1.4.0", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "pinia": "2.0.36", "pinia-plugin-persistedstate": "^3.2.3", "sortablejs": "^1.15.6", "tinycolor2": "^1.6.0", "vue": "3.5.10", "vue-i18n": "^9.14.3", "vue-json-schema-editor-visual": "^0.2.6", "vue-router": "^4.5.0", "vue-virtual-scroller": "2.0.0-beta.8", "vue3-colorpicker": "^2.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@antfu/eslint-config": "4.11.0", "@babel/core": "^7.26.10", "@iconify-json/ant-design": "^1.2.5", "@iconify-json/carbon": "^1.2.8", "@iconify-json/fluent": "^1.2.16", "@iconify-json/formkit": "^1.2.2", "@iconify-json/material-symbols": "^1.2.17", "@iconify-json/material-symbols-light": "^1.2.17", "@iconify-json/mdi": "^1.2.3", "@iconify-json/pepicons-pencil": "^1.2.1", "@iconify-json/ri": "^1.2.5", "@iconify-json/solar": "^1.2.2", "@iconify-json/teenyicons": "^1.2.2", "@tsconfig/node-lts-strictest-esm": "^18.12.1", "@types/fs-extra": "^9.0.13", "@types/lodash-es": "^4.17.12", "@types/node": "^18.19.82", "@types/tinycolor2": "^1.4.6", "@unocss/eslint-config": "^66.0.0", "@unocss/preset-icons": "^66.0.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "4.1.2", "@vue/compiler-sfc": "^3.5.13", "colors": "^1.4.0", "core-js": "^3.41.0", "eslint": "9.24.0", "eslint-plugin-format": "^1.0.1", "fs-extra": "^11.3.0", "highlight.js": "^11.11.1", "less": "^4.2.2", "lint-staged": "15.5.0", "path": "^0.12.7", "postcss": "^8.5.3", "prettier": "^3.5.3", "simple-git-hooks": "^2.12.1", "swagger-generator-api": "^1.0.29", "ts-node": "^10.9.2", "typescript": "^5.8.2", "unocss": "^66.0.0", "unplugin-auto-import": "19.1.1", "unplugin-vue-components": "28.4.1", "unplugin-vue-router": "^0.10.9", "vite": "6.2.2", "vite-bundle-visualizer": "1.2.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "3.2.1", "vite-plugin-vue-devtools": "^7.7.2", "vite-plugin-vue-layouts": "^0.11.0", "vue-component-type-helpers": "2.2.8"}, "overrides": {"@babel/helper-module-imports": "~7.22.15"}, "pnpm": {"overrides": {"@babel/helper-module-imports": "~7.22.15"}}, "resolutions": {"@babel/helper-module-imports": "~7.22.15"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*.{js,ts,vue,jsx,tsx}": "eslint --fix --cache"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}